import pulp

#定义问题实例
prob = pulp.LpProblem('Practice2_1',pulp.LpMaximize)

#定义决策变量
x_1 = pulp.LpVariable('x_1',lowBound = 0,cat = 'Binary')
x_2 = pulp.LpVariable('x_2',lowBound = 0,cat = 'Binary')
x_3 = pulp.LpVariable('x_3',lowBound = 0,cat = 'Binary')
z = pulp.LpVariable('z',lowBound = 0,cat = 'Binary')

#定义目标函数
prob += x_1 + z + x_3,"Objective"

#定义约束条件
prob += -2 * x_1 + 3 * x_2 + x_3 <= 3,"Constraint1"

#线性化约束
prob += z <= x_1,"Linearization1"
prob += z <= x_2,"Linearization2"
prob += z >= x_1 + x_2 - 1,"Linearization3"

#求解问题
prob.solve()

#输出结果
print('Status:',pulp.LpStatus[prob.status])
print("Optimal solution:")
print(f"z = {z.varValue}")
print(f"x_1 = {x_1.varValue}")
print(f"x_2 = {x_2.varValue}")
print(f"x_3 = {x_3.varValue}")
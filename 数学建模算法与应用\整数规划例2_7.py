import numpy as np
import pulp
import matplotlib.pyplot as plt

#网点坐标
coord = np.array([[9.4888,5.6917],[8.7928,10.3868],[11.5960,3.9294],[11.5643,4.4325],[5.6756,9.9658],
         [9.8497,17.6632],[9.1756,6.1517],[13.1385,11.8569],[15.4663,8.8721],[15.5464,15.5868]])

#可视化网点分布
def distribution(coord):
    x = coord[:,0]
    y = coord[:,1]
    plt.scatter(x,y,c = 'r',s = 5)
    plt.title("distribution")
    plt.xlabel("x")
    plt.ylabel("y")
    plt.show()

#定义距离矩阵
distance = np.zeros((10,10))
for i in range(10):
    for j in range(10):
        distance[i,j] = np.sqrt((coord[i,0] - coord[j,0]) ** 2 + (coord[i,1] - coord[j,1]) ** 2)

#定义问题实例
prob = pulp.LpProblem('Practice2_7',pulp.LpMinimize)

#定义变量
x = pulp.LpVariable.dicts('x',range(10),lowBound=0,cat='Binary')
y = pulp.LpVariable.dicts('y',[(i,j) for i in range(10) for j in range(10)],lowBound=0,cat='Binary')

#定义目标函数
prob += pulp.lpSum(x[i] for i in range(10)),"Objective"

# 定义约束条件
for j in range(10):
    prob += pulp.lpSum(y[(i,j)] for i in range(10)) >= 1, f"Constraint_1_{j+1}"

for i in range(10):
    for j in range(10):
        prob += distance[(i,j)] * y[(i,j)] <= 10 * x[i], f"Constraint_2_{i+1}_{j+1}"

for i in range(10):
    prob += pulp.lpSum(y[(i,j)] for j in range(10)) <= 5, f"Constraint_3_{i+1}"

for i in range(10):
    for j in range(10):
        prob += x[i] >= y[(i,j)], f"Constraint_4_{i+1}_{j+1}"
    
#求解问题
prob.solve()

#输出结果
print('Status:',pulp.LpStatus[prob.status])
print("Optimal solution:")
for i in range(10):
        if x[i].varValue == 1:
            print(f"x_{i+1} = {x[i].varValue}")
for i in range(10):
    for j in range(10):
        if y[(i,j)].varValue == 1:
            print(f"y_{i+1}_{j+1} = {y[(i,j)].varValue}")

distribution(coord)
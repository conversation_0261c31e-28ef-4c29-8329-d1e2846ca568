import win32com.client

def update_excel(input_file, 定价, 印数, 印张, 正文色数, 装订样式, 印刷方式):
    excel = win32com.client.Dispatch("Excel.Application")
    excel.Visible = False
    workbook_COM = excel.Workbooks.Open(input_file)
    sheet_COM = workbook_COM.ActiveSheet

    sheet_COM.Range("C4").Value = 定价
    sheet_COM.Range("E4").Value = 印数
    sheet_COM.Range("C6").Value = 印张
    sheet_COM.Range("E6").Value = 正文色数
    sheet_COM.Range("C7").Value = 装订样式
    sheet_COM.Range("E7").Value = 印刷方式

    d13_value = sheet_COM.Range("D13").Value
    
    workbook_COM.Close(SaveChanges=False)
    excel.Quit()
    
    return d13_value
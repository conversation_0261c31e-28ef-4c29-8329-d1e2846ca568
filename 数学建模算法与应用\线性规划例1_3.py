import numpy as np
from scipy.optimize import linprog

#定义目标函数系数
#linprog()默认最小化问题，所以目标函数系数取负值，以转化为最大化问题
c = np.array([-2,-3,5])

#定义不等式约束系数
#A_ub * x <= b_ub
A_ub = np.array([[-2,5,-1],[1,3,1]])
b_ub = np.array([-10,12])

#定义等式约束系数
#A_eq * x = b_eq
A_eq = np.array([[1,1,1]])
b_eq = np.array([7])

#定义决策变量边界
bounds = ((0,None),(0,None),(0,None))

#求解线性规划问题
result = linprog(c,A_ub = A_ub,b_ub = b_ub,A_eq = A_eq,b_eq = b_eq,bounds = bounds,method = 'highs')

#输出结果
x_1,x_2,x_3 = result.x
max_z = -result.fun
print('x_1 =',x_1)
print('x_2 =',x_2)
print('x_3 =',x_3)
print('max_z =',max_z)
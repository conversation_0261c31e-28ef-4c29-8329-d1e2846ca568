import numpy as np
from scipy.optimize import linprog

#定义运输单价（成本矩阵）
c_matrix = np.array([[6,2,6,7,4,2,5,9],
              [4,9,5,3,8,5,8,2],
              [5,2,1,9,7,4,3,3],
              [7,6,7,3,9,2,7,1],
              [2,3,9,5,7,2,6,5],
              [5,5,2,2,8,1,4,3]])

#定义销地需求量
d = np.array([35,37,22,32,41,32,43,38])

#定义产地产量
e = np.array([60,55,51,43,41,52])

#展平成本矩阵
c = c_matrix.flatten()

#定义等式约束系数
A_eq = np.zeros((8,48))
for i in range(8):
    for j in range(6):
        A_eq[i,j * 8 + i] = 1
b_eq = d

#定义不等式约束系数
A_ub = np.zeros((6,48))
for i in range(6):
    start_ub = i * 8
    end_ub = (i + 1) * 8
    A_ub[i,start_ub:end_ub] = 1
b_ub = e

#定义决策变量边界
bounds = [(0,None)] * 48

#求解线性规划问题
result = linprog(c,A_ub = A_ub,b_ub = b_ub,A_eq = A_eq,b_eq = b_eq,bounds = bounds,method = 'highs')

#输出结果
x = result.x
min_z = result.fun
print('min_z =',min_z)
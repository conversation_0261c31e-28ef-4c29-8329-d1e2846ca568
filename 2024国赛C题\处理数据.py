import pandas as pd

# 读取附件二的Excel文件，指定工作表
file_path = 'D:/Mathematical Modeling/2024国赛C题/附件2.xlsx'
sheet_name = '2023年统计的相关数据'
df = pd.read_excel(file_path, sheet_name=sheet_name)

# 打印列名，检查实际列名
print("列名如下：")
print(df.columns.tolist())

# 去除列名中的前后空格
df.columns = df.columns.str.strip()

# 检查是否仍然报错
try:
    # 提取价格区间的最小值和最大值
    df[['最低价格', '最高价格']] = df['销售单价/(元/斤)'].str.split('-', expand=True).apply(lambda x: x.str.strip())
    df['最低价格'] = df['最低价格'].astype(float)
    df['最高价格'] = df['最高价格'].astype(float)

    # 计算每亩最低收入和最高收入
    df['每亩最低收入'] = df['亩产量/斤'] * df['最低价格'] - df['种植成本/(元/亩)']
    df['每亩最高收入'] = df['亩产量/斤'] * df['最高价格'] - df['种植成本/(元/亩)']

    # 计算每亩平均收入
    df['每亩平均收入'] = (df['每亩最低收入'] + df['每亩最高收入']) / 2

    # 保存结果到新的Excel文件
    output_file_path = 'D:/Mathematical Modeling/2024国赛C题/附件2_处理.xlsx'
    df.to_excel(output_file_path, index=False)

    print(f"结果已保存到 {output_file_path}")
except KeyError as e:
    print(f"列名错误：{e}")
    print("请检查文件中的列名是否正确。")
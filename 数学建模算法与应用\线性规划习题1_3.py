
import numpy as np
import pulp

#利润
profit = [1,1.65,2.3]
#有效台时
value_time = [6000,10000,4000,7000,4000]
#设备费用
cost = [0.05,0.0321,0.0625,0.112,0.05]
#设备制造使用时长表
use_time_1 = [[5,6],[5,4],[5,7],[7,6],[7,4],[7,7]]
use_time_2 = [[10,8],[9,8]]
use_time_3 = [12,11]


#定义问题实例
prob = pulp.LpProblem('Practice1_3',pulp.LpMaximize)

#定义决策变量（x_i_jk）
x_1 = pulp.LpVariable.dicts('x_1',range(6),lowBound = 0,cat = 'Integer')    #x_1_11,x_1_12,x_1_13,x_1_21,x_1_22,x_1_23
x_2 = pulp.LpVariable.dicts('x_2',range(2),lowBound = 0,cat = 'Integer')    #x_2_11,x_2_21
x_3 = pulp.LpVariable('x_3',lowBound = 0,cat = 'Integer')                   #x_3_22

# 定义目标函数
# 收入部分
income = (profit[0] * sum(x_1[i] for i in range(6)) +
          profit[1] * sum(x_2[i] for i in range(2)) +
          profit[2] * x_3)

# 成本部分
cost_A = cost[0] * (5 * sum(x_1[i] for i in [0,1,2]) + 10 * x_2[0])
cost_B = cost[1] * (7 * sum(x_1[i] for i in [3,4,5]) + 9 * x_2[1] + 12 * x_3)
cost_C = cost[2] * (6 * sum(x_1[i] for i in [0,3]) + 8 * sum(x_2[i] for i in range(2)))
cost_D = cost[3] * (4 * sum(x_1[i] for i in [1,4]) + 11 * x_3)
cost_E = cost[4] * 7 * sum(x_1[i] for i in [2,5])

prob += income - (cost_A + cost_B + cost_C + cost_D + cost_E), "Objective"

#定义约束条件
prob += 5 * (x_1[0] + x_1[1] + x_1[2]) + 10 * x_2[0] <= 6000,"Constraint1"
prob += 7 * (x_1[3] + x_1[4] + x_1[5]) + 9 * x_2[1] + 12 * x_3 <= 10000,"Constraint2"
prob += 6 * (x_1[0] + x_1[3]) + 8 * (x_2[0] + x_2[1]) <= 4000,"Constraint3"
prob += 4 * (x_1[1] + x_1[4]) + 11 * x_3 <= 7000,"Constraint4"
prob += 7 * (x_1[2] + x_1[5]) <= 4000,"Constraint5"

#求解问题
prob.solve()

#输出结果
print('Status:',pulp.LpStatus[prob.status])
print("Optimal solution:")
for i in range(6):
    print(f"x_1_{i+1} = {x_1[i].varValue}")
for i in range(2):
    print(f"x_2_{i+1} = {x_2[i].varValue}")
print(f"x_3 = {x_3.varValue}")
print(f"max_z = {pulp.value(prob.objective)}")
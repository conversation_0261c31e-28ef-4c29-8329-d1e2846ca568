import pulp
from 图书印刷成本 import update_excel

y = 173012
p = 0.0156

定价 = 10.6
印张 = 7.5
正文色数 = '双色'
input_file = "D:/数学建模/2021北京联赛A题/附件4-图书印制成本计算表.xlsx"

# 定义问题实例
problem = pulp.LpProblem("Maximize Profit", pulp.LpMaximize)

# 定义变量
x1 = pulp.LpVariable("x1", lowBound=0.5*y, upBound=0.6*y, cat="Integer")
x2 = pulp.LpVariable("x2", lowBound=0.3*y, upBound=0.4*y, cat="Integer") 
x3 = pulp.LpVariable("x3", lowBound=0.1*y, upBound=0.3*y, cat="Integer")

# 构建初步目标函数(不含成本)
problem += (y * 定价 * 0.52) - (1 - p) * 定价 * 0.0273, "Total Profit"

# 约束条件
problem += x1 + x2 + x3 == y, "Total Constraint"

# 求解问题
problem.solve()

# 求解后计算实际成本
def calculate_cost(x):
    num = pulp.value(x)
    装订样式 = "胶订<10000册" if num < 10000 else "胶订≥10000册"
    印刷方式 = "平台" if num < 10000 else "轮转"
    return update_excel(input_file, 定价, num, 印张, 正文色数, 装订样式, 印刷方式)

cost1 = calculate_cost(x1)
cost2 = calculate_cost(x2)
cost3 = calculate_cost(x3)

# 计算最终利润
total_profit = (y * 定价 * 0.52) - cost1 - cost2 - cost3 - (1 - p) * 定价 * 0.0273

# 输出结果
print("Status:", pulp.LpStatus[problem.status])
print("Total Profit:", total_profit)
print("x1:", pulp.value(x1))
print("x2:", pulp.value(x2))
print("x3:", pulp.value(x3))
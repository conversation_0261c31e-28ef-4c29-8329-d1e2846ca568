# 第三问：使用随机森林算法预测Y染色体浓度检测结果
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, accuracy_score, r2_score, mean_squared_error
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def random_forest_classification(df):
    """随机森林分类：预测Y染色体浓度是否达标"""
    # 数据预处理
    df_clean = df.dropna(subset=['检测孕周', '孕妇BMI', 'Y染色体浓度', '年龄', 'GC含量'])

    # 特征选择
    features = ['检测孕周', '孕妇BMI', '年龄', 'GC含量', '原始读段数']
    X = df_clean[features]
    y = (df_clean['Y染色体浓度'] > 0.04).astype(int)  # 目标变量：是否达标

    # 数据分割和模型训练
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    rf_clf = RandomForestClassifier(n_estimators=100, random_state=42, max_depth=10)
    rf_clf.fit(X_train, y_train)

    # 预测和评估
    y_pred = rf_clf.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)

    print("=== 随机森林分类结果 ===")
    print(f"准确率: {accuracy:.4f}")
    print(classification_report(y_test, y_pred))

    # 特征重要性
    feature_importance = pd.DataFrame({
        'feature': features,
        'importance': rf_clf.feature_importances_
    }).sort_values('importance', ascending=False)
    print("\n特征重要性:")
    print(feature_importance)

    # 可视化特征重要性
    plt.figure(figsize=(8, 5))
    sns.barplot(data=feature_importance, x='importance', y='feature')
    plt.title('随机森林分类特征重要性')
    plt.xlabel('重要性')
    plt.tight_layout()
    plt.show()

    return rf_clf

def random_forest_regression(df):
    """随机森林回归：预测Y染色体浓度"""
    # 数据预处理
    df_clean = df.dropna(subset=['检测孕周', '孕妇BMI', 'Y染色体浓度', '年龄', 'GC含量'])

    # 特征选择
    features = ['检测孕周', '孕妇BMI', '年龄', 'GC含量', '原始读段数']
    X = df_clean[features]
    y = df_clean['Y染色体浓度']

    # 数据分割
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

    # 随机森林回归器
    rf_reg = RandomForestRegressor(n_estimators=100, random_state=42, max_depth=10)
    rf_reg.fit(X_train, y_train)

    # 预测
    y_pred = rf_reg.predict(X_test)

    # 评估
    r2 = r2_score(y_test, y_pred)
    mse = mean_squared_error(y_test, y_pred)
    rmse = np.sqrt(mse)

    print("\n=== 随机森林回归结果 ===")
    print(f"R²: {r2:.4f}")
    print(f"RMSE: {rmse:.4f}")

    # 预测vs实际值图
    plt.figure(figsize=(8, 6))
    plt.scatter(y_test, y_pred, alpha=0.6)
    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
    plt.xlabel('实际Y染色体浓度')
    plt.ylabel('预测Y染色体浓度')
    plt.title(f'随机森林回归预测结果 (R² = {r2:.4f})')
    plt.tight_layout()
    plt.show()

    return rf_reg

def predict_optimal_detection_time(df):
    """预测最佳检测时间"""
    # 创建训练数据：每个孕妇的最佳检测时间
    df_clean = df.dropna(subset=['孕妇代码', '检测孕周', '孕妇BMI', 'Y染色体浓度'])

    # 按孕妇分组，找到Y染色体浓度首次超过0.04的时间
    optimal_times = []
    for code, group in df_clean.groupby('孕妇代码'):
        group = group.sort_values('检测孕周')
        mask = group['Y染色体浓度'] > 0.04
        if mask.any():
            optimal_time = group[mask]['检测孕周'].iloc[0]
        else:
            optimal_time = group['检测孕周'].iloc[-1]  # 如果从未达标，使用最后检测时间

        # 获取该孕妇的基本信息
        info = group.iloc[0]
        optimal_times.append({
            '孕妇BMI': info['孕妇BMI'],
            '年龄': info['年龄'],
            'GC含量': info['GC含量'],
            '最佳检测时间': optimal_time
        })

    opt_df = pd.DataFrame(optimal_times)
    opt_df = opt_df.dropna()

    # 特征和目标
    features = ['孕妇BMI', '年龄', 'GC含量']
    X = opt_df[features]
    y = opt_df['最佳检测时间']

    # 训练随机森林
    rf_time = RandomForestRegressor(n_estimators=100, random_state=42)
    rf_time.fit(X, y)

    print("\n=== 最佳检测时间预测模型 ===")
    print(f"训练样本数: {len(opt_df)}")
    print(f"模型R²: {rf_time.score(X, y):.4f}")

    # 特征重要性
    importance = pd.DataFrame({
        'feature': features,
        'importance': rf_time.feature_importances_
    }).sort_values('importance', ascending=False)
    print("\n特征重要性:")
    print(importance)

    return rf_time, opt_df

def main():
    """主函数"""
    print("第三问：随机森林算法分析")

    # 加载数据
    df = pd.read_excel(r'D:\数学建模\2025国赛C题\男胎数据.xlsx')
    print(f"数据形状: {df.shape}")

    # 1. 分类任务：预测Y染色体浓度是否达标
    random_forest_classification(df)

    # 2. 回归任务：预测Y染色体浓度
    random_forest_regression(df)

    # 3. 预测最佳检测时间
    rf_time, _ = predict_optimal_detection_time(df)

    # 示例预测
    print("\n=== 示例预测 ===")
    # 假设一个新孕妇：BMI=25, 年龄=30, GC含量=0.45
    sample = pd.DataFrame([[25, 30, 0.45]], columns=['孕妇BMI', '年龄', 'GC含量'])
    predicted_time = rf_time.predict(sample)[0]
    print(f"BMI=25, 年龄=30, GC含量=0.45的孕妇建议检测时间: {predicted_time:.1f}周")

if __name__ == '__main__':
    main()
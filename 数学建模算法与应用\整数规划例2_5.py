import pulp

#工人数量需求
need = [35,40,50,45,55,30]
#共6个班次
#0-8;4-12;8-16;12-20;16-24;20-4

#定义问题实例
prob = pulp.LpProblem('Practice2_5',pulp.LpMinimize)

#定义决策变量
x_1 = pulp.LpVariable('x_1',lowBound = 0,cat = 'Integer')
x_2 = pulp.LpVariable('x_2',lowBound = 0,cat = 'Integer')
x_3 = pulp.LpVariable('x_3',lowBound = 0,cat = 'Integer')
x_4 = pulp.LpVariable('x_4',lowBound = 0,cat = 'Integer')
x_5 = pulp.LpVariable('x_5',lowBound = 0,cat = 'Integer')
x_6 = pulp.LpVariable('x_6',lowBound = 0,cat = 'Integer')

#定义目标函数
prob += x_1 + x_2 + x_3 + x_4 + x_5 + x_6,"Objective"

#定义约束条件
prob += x_1 + x_6 >= need[0],"Constraint1"
prob += x_1 + x_2 >= need[1],"Constraint2"
prob += x_2 + x_3 >= need[2],"Constraint3"
prob += x_3 + x_4 >= need[3],"Constraint4"
prob += x_4 + x_5 >= need[4],"Constraint5"
prob += x_5 + x_6 >= need[5],"Constraint6"

#求解问题
prob.solve()

#输出结果
print('Status:',pulp.LpStatus[prob.status])
print("Optimal solution:")
print(f"x_1 = {x_1.varValue}")
print(f"x_2 = {x_2.varValue}")
print(f"x_3 = {x_3.varValue}")
print(f"x_4 = {x_4.varValue}")
print(f"x_5 = {x_5.varValue}")
print(f"x_6 = {x_6.varValue}")
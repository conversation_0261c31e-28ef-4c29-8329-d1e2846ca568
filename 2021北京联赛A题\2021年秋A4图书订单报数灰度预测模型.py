import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

# 数据
years = [2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020]
order_data = [101900, 109900, 116100, 131800, 134849, 141700, 148940, 164689]

# 1. 累加生成序列 (1-AGO)
x0 = np.array(order_data)
x1 = np.cumsum(x0)

# 2. 构建紧邻均值矩阵
B = np.zeros((len(x0)-1, 2))
for i in range(len(x0)-1):
    B[i, 0] = -0.5 * (x1[i] + x1[i+1])
    B[i, 1] = 1

# 3. 计算参数 a 和 b
Y = x0[1:].reshape(-1, 1)
a_b = np.linalg.inv(B.T @ B) @ B.T @ Y
a = a_b[0][0]
b = a_b[1][0]

# 4. 时间响应函数
def time_response(k):
    return (x0[0] - b/a) * np.exp(-a * k) + b/a

# 5. 还原预测值
x_hat = np.zeros_like(x0)
x_hat[0] = x0[0]
for i in range(1, len(x0)):
    x_hat[i] = time_response(i) - time_response(i-1)

# 6. 预测2021年
pred_2021 = time_response(len(x0)) - time_response(len(x0)-1)

# 7. 计算拟合精度
error = np.abs(x0 - x_hat)
relative_error = error / x0
avg_error = np.mean(relative_error)

# 8. 绘制结果
plt.figure(figsize=(10, 6))
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.plot(years, x0, 'o-', label='实际值')
plt.plot(years, x_hat, 'x--', label='预测值')
plt.xlabel('年份')
plt.ylabel('订单数')
plt.title(f'GM(1,1)预测结果 (平均相对误差:{avg_error:.2%})')
plt.legend()
plt.grid(True)
plt.show()

print(f"发展系数 a: {a:.6f}")
print(f"灰色作用量 b: {b:.2f}")
print(f"2021年预测订单数: {pred_2021:.2f}")
print(f"平均相对误差: {avg_error:.2%}")
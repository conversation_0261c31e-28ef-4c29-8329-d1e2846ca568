import numpy as np
import matplotlib.pyplot as plt

# 用户可修改参数
N0 = 100                # 初始菌群数量
K = 1e8                 # 环境容纳量
start_hour = 0        # 开始时刻(小时)
duration = 16           # 模拟时长(小时)
popt = [23.60590278, -3.3370821, -8.44419722, 0.0592769, 2.9214654, 0.73510765, -0.19006826]

# 温度相关函数
def fourier_model(x, *params):
    """24小时周期性温度模型"""
    a0, a1, b1, a2, b2, a3, b3 = params
    return a0 + a1*np.sin(1*np.pi*x/12) + b1*np.cos(1*np.pi*x/12) + \
           a2*np.sin(2*np.pi*x/12) + b2*np.cos(2*np.pi*x/12) + \
           a3*np.sin(3*np.pi*x/12) + b3*np.cos(3*np.pi*x/12)

def r_from_temp(T):
    """温度-增长率函数"""
    return -0.0002515*T**3 + 0.006988*T**2 + 0.09188*T - 0.4631

# 生成时间序列
t_hours = np.linspace(start_hour, start_hour + duration, 1000)  # 高精度时间轴
t_norm = t_hours % 24  # 转换为24小时周期内的标准化时间
temp_series = fourier_model(t_norm, *popt)  # 生成温度序列

# 计算菌群动态增长
N = [N0]
dt = t_hours[1] - t_hours[0]

for i in range(len(t_hours)-1):
    current_temp = temp_series[i]
    current_r = r_from_temp(current_temp)
    dN = current_r * N[i] * (1 - N[i]/K) * dt
    N.append(N[i] + dN)

# 可视化设置
plt.figure(figsize=(10,6))
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']

# 主坐标轴：菌群曲线
main_line, = plt.plot(t_hours - start_hour, N, 'b-', lw=2, 
                     label=f'菌群动态增长\nN0={N0}\nK={K:.1e}')
capacity_line = plt.axhline(K, color='r', linestyle='--', 
                          label=f'环境容量阈值')

# 次坐标轴：温度曲线
ax2 = plt.gca().twinx()
temp_line, = ax2.plot(t_hours - start_hour, temp_series, 
                     color='#FF8800', linestyle=':', lw=1.5, alpha=0.8,
                     label=f'温度波动曲线)')

# 图例
plt.legend(
    handles=[main_line, capacity_line, temp_line],
    loc='upper left',
    fontsize=10,
    frameon=True
)

# 坐标轴标注
plt.xlabel('模拟经过时间 (小时)', fontsize=12, labelpad=10)
plt.ylabel('菌群数量 (CFU/mL)', color='b', fontsize=12)
ax2.set_ylabel('环境温度 (℃)', color='#FF8800', fontsize=12)

# 网格和图题
plt.grid(alpha=0.2, linestyle='--')
plt.title(f'动态温度驱动的病原菌生长模拟\n'
         f'起始时间: {start_hour:.1f}时 | 持续时长: {duration}小时',
         fontsize=14, pad=20)

plt.tight_layout()
plt.show()
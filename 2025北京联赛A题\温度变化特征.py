import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

# 读取Excel文件
file_path = 'D:/数学建模/2025北京联赛A题/附件1.xlsx'
df = pd.read_excel(file_path)

# 清洗列名（以保证后续处理顺利）
df.columns = ['时间', '温度(℃)', '相对湿度RH（%）']

# 转换时间格式
df['时间'] = df['时间'].apply(lambda x: datetime.strptime(x.strip(), '%H:%M:%S'))

# 绘制原始温度数据的可视化图
plt.figure(figsize=(12, 6))
#微软雅黑
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.plot(df['时间'], df['温度(℃)'], marker='o', linestyle='-', color='b', markersize=3)
plt.title('环境内温度24h变化图', fontsize=16)
plt.xlabel('时间', fontsize=12)
plt.ylabel('温度(℃)', fontsize=12)
plt.grid(True, linestyle='--', alpha=0.6)
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()

# 计算温度变化率
temperature_changes = df['温度(℃)'].diff()  # 计算相邻温度变化值
temperature_changes.dropna(inplace=True)  # 去除第一个NaN值（因为第一个点没有前一个点来计算变化率）
df['温度变化率(℃/5分钟)'] = temperature_changes  # 新增一列存放温度变化率

# 将带变化率的数据存入新的Excel文件
df.to_excel('D:/数学建模/2025北京联赛A题/温度变化速率.xlsx', index=False)

# 给新生成的那列数据缺失的第一个值添加为0
df.loc[0, '温度变化率(℃/5分钟)'] = 0  # 将第一个值设为0
df.to_excel('D:/数学建模/2025北京联赛A题/温度变化速率.xlsx', index=False)  # 保存修改后的文件

# 读取刚生成的温度变化率数据文件
new_df = pd.read_excel('D:/数学建模/2025北京联赛A题/温度变化速率.xlsx')

# 绘制温度变化率的可视化图
plt.figure(figsize=(12, 6))
# 将时间转换为数值格式
time_values = [t.hour + t.minute/60 + t.second/3600 for t in new_df['时间']]
plt.plot(time_values, new_df['温度变化率(℃/5分钟)'], 
         marker='o', linestyle='-', color='r', markersize=3)
plt.title('环境内温度变化率24h变化图', fontsize=16)
plt.xlabel('时间(小时)', fontsize=12)  # 修改x轴标签
plt.ylabel('温度变化率(℃/5分钟)', fontsize=12)
plt.grid(True, linestyle='--', alpha=0.6)
plt.xticks(np.arange(0, 24, 2), rotation=45)  # 设置x轴刻度为0-24小时
plt.tight_layout()
plt.show()
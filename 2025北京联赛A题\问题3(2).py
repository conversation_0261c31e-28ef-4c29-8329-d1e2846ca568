import numpy as np
import matplotlib.pyplot as plt

# 常数定义
h = 1  # 初始高度 (m)
n = 10000    # 细菌数量
v0 = 19.07  # 初始速度大小 (m/s)
v_perp_ratio = 0.1  # 垂直速度分量比例（10% of v0）
g = 9.81  # 重力加速度 (m/s²)
rho = 1.225  # 空气密度 (kg/m³)
Cd = 0.47  # 阻力系数（球体）
A = 5e-19  # 截面积 (m²)
m = 6e-16  # 质量 (kg)

# 计算垂直速度分量
v_perp = v0 * v_perp_ratio

# 计算空气阻力系数
k_air = 0.5 * rho * Cd * A / m

# 生成单位向量
def vector_to_unit(x=None, y=None, z=None):
    if x is None or y is None or z is None:
        vec = np.random.randn(3)
    else:
        vec = np.array([x, y, z], dtype=float)
    norm = np.linalg.norm(vec)
    return vec / norm if norm > 1e-8 else np.array([1, 0, 0])

# 生成与给定向量正交的随机单位向量
def get_orthogonal_vector(u):
    while True:
        v = np.random.randn(3)
        v -= np.dot(v, u) * u
        if np.linalg.norm(v) < 1e-8:
            continue
        return v / np.linalg.norm(v)

# 初始化存储结构
dt = 0.01
steps = int(10 / dt)
positions = np.zeros((n, steps+1, 3))
velocities = np.zeros((n, steps+1, 3))
landing_positions = np.zeros((n, 2))
landed = np.zeros(n, dtype=bool)

# 模拟每个细菌的运动
for i in range(n):
    # 为每个细菌生成随机初始方向
    u_direction = vector_to_unit()
    v_perp_dir = get_orthogonal_vector(u_direction)
    
    # 设置初始速度和位置
    velocities[i, 0] = v0 * u_direction + v_perp * v_perp_dir
    positions[i, 0, 2] = h

# 运动模拟
for step in range(steps):
    for i in range(n):
        if landed[i]: continue
        
        vel = velocities[i, step]
        pos = positions[i, step]
        
        # 计算空气阻力
        v_mag = np.linalg.norm(vel)
        a_air = -k_air * v_mag * vel / max(v_mag, 1e-8)
        
        # 添加湍流
        u_turb = vector_to_unit()
        a_turb = 0.1 * u_turb
        
        # 更新状态
        new_pos = pos + vel * dt
        new_vel = vel + (a_air + a_turb - np.array([0,0,g])) * dt
        
        # 处理地面碰撞
        if new_pos[2] < 0:
            new_pos[2] = 0
            new_vel = np.zeros(3)
            landed[i] = True
            landing_positions[i] = new_pos[:2]
        
        positions[i, step+1] = new_pos
        velocities[i, step+1] = new_vel

# 绘制俯视落点分布
plt.figure(figsize=(10, 8))
plt.scatter(landing_positions[:,0], landing_positions[:,1], 
           c=np.arange(n), cmap='viridis', alpha=0.7, s=10)
plt.xlabel('X Position (m)')
plt.ylabel('Y Position (m)')
plt.title('Bacterial Landing Positions Top View')
plt.colorbar(label='Bacterium ID')
plt.grid(True)
plt.show()
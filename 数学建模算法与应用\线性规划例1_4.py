import pulp

#创建问题实例
#设置为最小化问题
prob = pulp.LpProblem('Example1_4',pulp.LpMinimize)

#定义决策变量
x_11 = pulp.LpVariable('x_11',lowBound = 0,cat= 'Integer')
x_12 = pulp.LpVariable('x_12',lowBound = 0,cat= 'Integer')
x_13 = pulp.LpVariable('x_13',lowBound = 0,cat= 'Integer')
x_14 = pulp.LpVariable('x_14',lowBound = 0,cat= 'Integer')
x_21 = pulp.LpVariable('x_21',lowBound = 0,cat= 'Integer')
x_22 = pulp.LpVariable('x_22',lowBound = 0,cat= 'Integer')
x_23 = pulp.LpVariable('x_23',lowBound = 0,cat= 'Integer')
x_31 = pulp.LpVariable('x_31',lowBound = 0,cat= 'Integer')
x_32 = pulp.LpVariable('x_32',lowBound = 0,cat= 'Integer')
x_41 = pulp.LpVariable('x_41',lowBound = 0,cat= 'Integer')

#定义目标函数
prob += 2800 * (x_11 +x_21 + x_31 + x_41) + 4500 * (x_12 + x_22 + x_32) + 6000 * (x_13 + x_23) + 7300 * (x_14),"Objective"

#定义约束条件
prob += x_11 + x_12 + x_13 + x_14 >= 15,"Constraint1"
prob += x_12 + x_13 + x_14 + x_21 + x_22 + x_23 >= 10,"Constraint2"
prob += x_13 + x_14 + x_22 + x_23 + x_31 + x_32 >= 20,"Constraint3"
prob += x_14 + x_23 + x_32 + x_41 >= 12,"Constraint4"

#求解问题
prob.solve()

#输出结果
print('Status:',pulp.LpStatus[prob.status])
print("Optimal solution:")
print(f"x_11 = {x_11.varValue}")
print(f"x_12 = {x_12.varValue}")
print(f"x_13 = {x_13.varValue}")
print(f"x_14 = {x_14.varValue}")
print(f"x_21 = {x_21.varValue}")
print(f"x_22 = {x_22.varValue}")
print(f"x_23 = {x_23.varValue}")
print(f"x_31 = {x_31.varValue}")
print(f"x_32 = {x_32.varValue}")
print(f"x_41 = {x_41.varValue}")
print(f"min_z = {pulp.value(prob.objective)}")
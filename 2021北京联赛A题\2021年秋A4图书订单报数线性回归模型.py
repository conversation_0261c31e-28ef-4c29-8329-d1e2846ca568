import numpy as np
import pandas as pd
from sklearn.linear_model import LinearRegression
import matplotlib.pyplot as plt

# 数据准备
years = np.array([2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020])[:, np.newaxis]
values = np.array([101900, 109900, 116100, 131800, 134849, 141700, 148940, 164689])

# 创建线性回归模型
model = LinearRegression()

# 训练模型
model.fit(years, values)

# 获取回归系数和截距
slope = model.coef_[0]
intercept = model.intercept_

# 预测2021年的值
year_2021 = np.array([[2021]])
predicted_2021 = model.predict(year_2021)[0]

# 打印预测结果
print(f"2021年的预测值: {predicted_2021:.2f}")
print(f"回归方程: y = {slope:.2f}x + {intercept:.2f}")

# 可视化结果
plt.figure(figsize=(10, 6))
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # 设置中文字体

plt.scatter(years, values, color='#1f77b4', s=80, label='历史数据')
plt.plot(years, model.predict(years), color='#ff7f0e', linewidth=2, label='回归线')
plt.scatter(2021, predicted_2021, color='#2ca02c', s=100, label='2021预测')

plt.xlabel('年份', fontsize=12)
plt.ylabel('订单数', fontsize=12)
plt.title('A类图书订单数线性回归预测', fontsize=14, pad=20)
plt.grid(True, linestyle=':', alpha=0.6)
plt.legend(loc='upper left', frameon=True)

plt.tight_layout()
plt.show()
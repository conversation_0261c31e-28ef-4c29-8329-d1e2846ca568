import pulp

#创建问题实例
prob = pulp.LpProblem('Practice1_1',pulp.LpMaximize)

#定义决策变量
x_1 = pulp.LpVariable('x_1',lowBound = 0)
x_2 = pulp.LpVariable('x_2',lowBound = 0)
x_3 = pulp.LpVariable('x_3',lowBound = 0)

#定义目标函数
prob += 3 * x_1 - x_2 - x_3,"Objective"

#定义约束条件
prob += x_1 - 2 * x_2 + x_3 <= 11,"Constraint1"
prob += -4 * x_1 + x_2 + 2 * x_3 >= 3,"Constraint2" 
prob += -2 * x_1 + x_3 == 1,"Constraint3"

#求解问题
prob.solve()

#输出结果
print('Status:',pulp.LpStatus[prob.status])
print("Optimal solution:")
print(f"x_1 = {x_1.varValue}")
print(f"x_2 = {x_2.varValue}")
print(f"x_3 = {x_3.varValue}")
print(f"max_z = {pulp.value(prob.objective)}")
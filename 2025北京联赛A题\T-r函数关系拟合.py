import numpy as np
import matplotlib.pyplot as plt

# 给定数据
temperatures_C = np.array([4, 20, 25, 28, 30])  # 摄氏温度(℃)
r = np.array([0, 2.1626, 2.2545, 2.0931, 1.7825])  # 增殖速率(h⁻¹)

# 进行多项式拟合（改为3次多项式，更稳定）
coefficients = np.polyfit(temperatures_C, r, 3)
p = np.poly1d(coefficients)

# 生成用于绘图的温度值（扩展范围）
x_fit = np.linspace(4, 35, 200)  # 从0℃到35℃
y_fit = p(x_fit)

# 绘制图形
plt.figure(figsize=(10, 6))
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 绘制原始数据点
plt.scatter(temperatures_C, r, color='blue', s=100, label='实验数据点', zorder=5)

# 绘制拟合曲线
plt.plot(x_fit, y_fit, color='red', linewidth=2.5, label=f'三次多项式拟合\nR²={1-np.var(r-p(temperatures_C))/np.var(r):.3f}')

# 添加图形元素
plt.title('温度与增殖速率的关系', fontsize=14, pad=20)
plt.xlabel('温度 (℃)', fontsize=12)
plt.ylabel('增殖速率 (h⁻¹)', fontsize=12)
plt.grid(alpha=0.3, linestyle='--')
plt.legend(fontsize=10, framealpha=0.9)
plt.ylim(-0.5, 3.0)  # 设置y轴范围

# 显示图形
plt.tight_layout()
plt.show()

# 输出拟合多项式
print("拟合多项式为：")
print(p)
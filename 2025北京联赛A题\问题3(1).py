import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

# 常数定义
S = 100
h = 1  # 初始高度 (m)
n = S * 3    # 细菌数量
v0 = 19.07  # 初始速度大小 (m/s)
v_perp_ratio = 0.1  # 垂直速度分量比例（10% of v0）
g = 9.81  # 重力加速度 (m/s²)
rho = 1.225  # 空气密度 (kg/m³)
Cd = 0.47  # 阻力系数（球体）
A = 5e-19  # 截面积 (m²)，假设体积为5e-13 cm³转换而来
m = 6e-16  # 质量 (kg)

x = 2
y = 3
z = 5

# 计算垂直速度分量
v_perp = v0 * v_perp_ratio

# 计算空气阻力系数
k_air = 0.5 * rho * Cd * A / m

# 生成单位向量
def vector_to_unit(x=None, y=None, z=None):
    """
    将输入坐标转换为单位向量
    :param x: x坐标值，若为None则随机生成
    :param y: y坐标值，若为None则随机生成
    :param z: z坐标值，若为None则随机生成
    :return: 单位向量(numpy数组)
    """
    if x is None or y is None or z is None:
        vec = np.random.randn(3)  # 随机生成向量
    else:
        vec = np.array([x, y, z], dtype=float)
    norm = np.linalg.norm(vec)
    if norm < 1e-8:  # 避免除以0
        return np.array([1, 0, 0])  # 返回默认单位向量
    return vec / norm

# 生成与给定向量正交的随机单位向量
def get_orthogonal_vector(u):
    while True:
        v = np.random.randn(3)
        v -= np.dot(v, u) * u  # 投影到正交补空间
        if np.linalg.norm(v) < 1e-8:
            continue
        v /= np.linalg.norm(v)
        return v

# 初始化位置、速度和落点记录器
dt = 0.01  # 时间步长 (s)
steps = int(10 / dt)  # 模拟总步数
positions = np.zeros((n, steps+1, 3))
velocities = np.zeros((n, steps+1, 3))
landing_positions = np.zeros((n, 2))  # 记录x-y平面落点
landed = np.zeros(n, dtype=bool)     # 落地标记数组

# 设置初始位置和速度
u_direction = vector_to_unit(x, y, z)

for i in range(n):
    v_perp_dir = get_orthogonal_vector(u_direction)
    velocities[i, 0] = v0 * u_direction + v_perp * v_perp_dir
    positions[i, 0, 2] = h

# 模拟运动
for step in range(steps):
    for i in range(n):
        if landed[i]:
            continue
        
        vel = velocities[i, step]
        pos = positions[i, step]
        
        # 计算空气阻力
        v_mag = np.linalg.norm(vel)
        a_air = -k_air * v_mag * vel / max(v_mag, 1e-8)
        
        # 湍流加速度
        u_turb = vector_to_unit()
        a_turb = 0.1 * u_turb
        
        # 总加速度
        a_total = np.array([0, 0, -g]) + a_air + a_turb
        
        # 更新状态
        new_pos = pos + vel * dt
        new_vel = vel + a_total * dt
        
        # 地面碰撞处理
        if new_pos[2] < 0:
            new_pos[2] = 0
            new_vel = np.zeros(3)
            landed[i] = True
            landing_positions[i] = new_pos[:2]  # 记录落点坐标
        
        positions[i, step+1] = new_pos
        velocities[i, step+1] = new_vel

# 创建并排可视化
fig = plt.figure(figsize=(18, 8))

# 3D轨迹图
ax1 = fig.add_subplot(121, projection='3d')
for i in range(n):
    valid = positions[i, :, 2] >= 0
    ax1.plot(positions[i, valid, 0], 
            positions[i, valid, 1], 
            positions[i, valid, 2], 
            marker='o', markersize=1)
ax1.set_xlabel('X (m)')
ax1.set_ylabel('Y (m)')
ax1.set_zlabel('Z (m)')
ax1.set_title('3D Trajectories')

# 俯视图散点图
ax2 = fig.add_subplot(122)
ax2.scatter(landing_positions[:, 0], 
           landing_positions[:, 1], 
           s=10, c=np.arange(n), cmap='viridis', alpha=0.7)
ax2.set_xlabel('X (m)')
ax2.set_ylabel('Y (m)')
ax2.set_title('Top View of Landing Points')
ax2.grid(True)
plt.colorbar(ax2.scatter(landing_positions[:, 0], 
                        landing_positions[:, 1], 
                        c=np.arange(n), 
                        cmap='viridis'), 
            label='Bacterium ID')

plt.tight_layout()
plt.show()
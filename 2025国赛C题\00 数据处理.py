import pandas as pd
import re


def precess1():
    for sheet_name in ['男胎检测数据', '女胎检测数据']:

        df = pd.read_excel(r'D:\数学建模\2025国赛C题\附件.xlsx', sheet_name=sheet_name)

        df = df.dropna(subset=['末次月经'])
        
        if 'GC含量' in df.columns:
            df = df[(df['GC含量'] >= 0.35) & (df['GC含量'] <= 0.65)]

        if 'IVF妊娠' in df.columns:
            df['IVF妊娠'] = df['IVF妊娠'].replace({
                '自然受孕': 1,
                'IUI（人工授精）': 0
            })

        if '胎儿是否健康' in df.columns:
            df['胎儿是否健康'] = df['胎儿是否健康'].replace({
                '是': 1,
                '否': 0
            })

        if '原始读段数' in df.columns:
            df = df[(df['原始读段数'] >= 3000000)]
        
        # 定义转换函数
        def convert_weeks(week_str):
            week_str = str(week_str)
            match = re.match(r'(\d+)w(?:\+(\d+))?', week_str)
            if match:
                weeks = int(match.group(1))
                days = int(match.group(2)) if match.group(2) else 0
                return round(weeks + days / 7, 3)
            return week_str
        
        # 应用转换函数
        df['检测孕周'] = df['检测孕周'].apply(convert_weeks)
        
        # 转换为数值类型，无法转换的设为NaN
        df['检测孕周'] = pd.to_numeric(df['检测孕周'], errors='coerce')

        if sheet_name == '男胎检测数据':
            df.to_excel(r'2025国赛C题\男胎数据.xlsx', index=False)
        else:
            df.to_excel(r'2025国赛C题\女胎数据.xlsx', index=False)

def precess2():
    df = pd.read_excel('D:/数学建模/2025国赛C题/男胎数据.xlsx')
    
    if '胎儿是否健康' in df.columns:
            df = df[(df['胎儿是否健康'] == 1)]

    # 按孕妇代码分组并排序
    df = df.sort_values(['孕妇代码', '检测孕周'])

    result = []

    for name, group in df.groupby('孕妇代码'):
        # 查找Y染色体浓度首次超过0.04的索引
        mask = group['Y染色体浓度'] > 0.04
        
        if mask.any():
            first_over_idx = mask.idxmax()
            over_row = group.loc[first_over_idx]
            result.append(over_row.to_dict())
        else:
            # 从未超过0.04的情况，记录最后一次检测
            last_row = group.iloc[-1]
            result.append(last_row.to_dict())

    # 转换为DataFrame并保存
    result_df = pd.DataFrame(result)
    result_df = result_df.drop(columns=['胎儿是否健康',"序号"])
    result_df.to_excel('D:/数学建模/2025国赛C题/数据B.xlsx', index=False, float_format='%.10g')

if __name__ == '__main__':
    # precess1()
    precess2()
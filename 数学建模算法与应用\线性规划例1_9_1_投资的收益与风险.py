import numpy as np
from scipy.optimize import linprog
import matplotlib.pyplot as plt

#资金
M = 10000
#平均收益率(r_i - p_i)
r = np.array([0.05,0.27,0.19,0.185,0.185])
#风险损失率
q = np.array([0,0.025,0.015,0.055,0.026])
#交易费费率(1 + p_i)
p = np.array([1,1.01,1.02,1.045,1.065])
#投资阈值忽略不计

#定义模型1
def model_1(M,r,q,p,a):
    #定义目标函数系数
    c = -r
    
    #定义不等式约束系数
    A_ub = np.eye(5) * q
    b_ub = np.full(5, a * M)
    
    #定义等式约束系数
    A_eq = p.reshape(1,-1)
    b_eq = np.array([M])

    #定义决策变量边界
    bounds = [(0, None)] * 5

    #求解线性规划问题
    result = linprog(c,A_ub = A_ub,b_ub = b_ub,A_eq = A_eq,b_eq = b_eq,bounds = bounds,method = 'highs')

    #输出结果
    x_0,x_1,x_2,x_3,x_4 = result.x
    Q = -result.fun
    return x_0,x_1,x_2,x_3,x_4,Q

#可视化模型1的结果
def plot1():
    #定义数据表
    Data = np.zeros((7,51))
    i = 0
    for a in np.arange(0,0.051,0.001):
        x_0,x_1,x_2,x_3,x_4,Q = model_1(M,r,q,p,a)
        new_data = np.array([a,x_0,x_1,x_2,x_3,x_4,Q])
        Data[:,i] = new_data
        i += 1

    #定义x和y轴数据
    x_data = Data[0,:]
    y_data = Data[6,:]
    
    #绘制散点图
    plt.scatter(x_data,y_data,c = 'r',s = 5)
    plt.title("Model 1")
    plt.xlabel("a")
    plt.ylabel("Q")

    #显示图表
    plt.show()

plot1()
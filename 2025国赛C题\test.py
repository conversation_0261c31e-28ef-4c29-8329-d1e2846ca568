import numpy as np
import pandas as pd
from sklearn.cluster import KMeans
import matplotlib.pyplot as plt
from lifelines import KaplanMeierFitter

df1 = pd.read_excel(r'D:\数学建模\2025国赛C题\数据B.xlsx')
df2 = pd.read_excel(r'D:\数学建模\2025国赛C题\男胎数据.xlsx')

# 微软雅黑字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']

# K-means聚类
def K_means(df):
    # 提取BMI数据
    X = df[['孕妇BMI']].values

    # 确定最佳K值（肘部法则）
    wcss = []
    for i in range(1, 11):
        kmeans = KMeans(n_clusters=i, init='k-means++', random_state=42)
        kmeans.fit(X)
        wcss.append(kmeans.inertia_)

    # 绘制肘部图
    plt.plot(range(1, 11), wcss)
    plt.title('肘部法则')
    plt.xlabel('聚类数')
    plt.ylabel('WCSS')
    plt.show()

    # 根据肘部图选择K值
    k = 3  # 请根据肘部图实际选择
    kmeans = KMeans(n_clusters=k, init='k-means++', random_state=42)
    y_kmeans = kmeans.fit_predict(X)

    # 将聚类结果添加到数据框
    df['BMI聚类'] = y_kmeans

    # 可视化聚类结果
    plt.scatter(df['孕妇代码'], df['孕妇BMI'], c=y_kmeans, s=40, cmap='viridis')
    plt.title('孕妇BMI聚类结果')
    plt.xlabel('孕妇代码')
    plt.ylabel('BMI')
    plt.xticks([])
    plt.show()

    # 保存聚类结果
    df.to_excel('D:/数学建模/2025国赛C题/聚类结果.xlsx', index=False)
    return df

# 生存分析
def KaplanMeier(df, cluster_col='BMI聚类'):
    # 创建达标状态列 (Y染色体浓度 > 0.04为达标)
    df['达标状态'] = (df['Y染色体浓度'] > 0.04).astype(int)
    
    df['达标孕周'] = df['检测孕周']
    
    kmf = KaplanMeierFitter()
    
    plt.figure(figsize=(10, 6))
    
    # 为每个聚类分别绘制KM曲线
    for cluster in sorted(df[cluster_col].unique()):
        mask = df[cluster_col] == cluster
        kmf.fit(df[mask]['达标孕周'], 
                event_observed=df[mask]['达标状态'],  # 1表示达标，0表示删失
                label=f'聚类{cluster}')
        kmf.plot_survival_function()
    
    plt.title('Kaplan-Meier生存曲线')
    plt.xlabel('孕周(T)')
    plt.ylabel('未达标概率 S(T)')
    plt.grid(True)
    plt.show()
    
    return kmf

def Risk(t, bmi_group, unhealthy_prob):
    # 为不同BMI分类设置不同的峰值周数
    peak_weeks = {0: 14, 1: 18, 2: 20} 
    
    if isinstance(t, (np.ndarray, list)):
        t = np.asarray(t)
        mean = peak_weeks[bmi_group]
        std = 3  # 标准差保持不变
        risk = np.exp(-0.5 * ((t - mean)/std)**2)
        # 检测过早的风险
        early_risk = np.where(t < 12, 5, 0)
        # 检测过晚的风险
        late_risk = np.where(t > 25, 10, 0)
        # 总风险
        total_risk = early_risk + late_risk + (1 - risk) * 9 * (1 + unhealthy_prob)
        return total_risk
    else:
        if t < 11 or t > 27:
            return 10
        mean = peak_weeks[bmi_group]
        std = 3
        risk = np.exp(-0.5 * ((t - mean)/std)**2)
        # 检测过早的风险
        early_risk = 5 if t < 12 else 0
        # 检测过晚的风险
        late_risk = 10 if t > 27 else 0
        # 总风险
        total_risk = early_risk + late_risk + (1 - risk) * 9 * (1 + unhealthy_prob)
        return total_risk
    
def FindTime(df, df2, bmi_ranges=[(20,32),(32,36),(36,45)]):
    optimal_times = {}

    unhealthy_prob = df2['胎儿是否健康'].value_counts(normalize=True).get(0, 0)
    
    for i, (lower, upper) in enumerate(bmi_ranges):
        # 筛选当前BMI分类的数据
        bmi_group = df[(df['孕妇BMI'] >= lower) & (df['孕妇BMI'] < upper)]
        
        # 创建达标状态和达标孕周列
        bmi_group = bmi_group.copy()
        bmi_group['达标状态'] = (bmi_group['Y染色体浓度'] > 0.04).astype(int)
        bmi_group['达标孕周'] = bmi_group['检测孕周']

        # 清理NaN值
        bmi_group = bmi_group.dropna(subset=['达标孕周', '达标状态'])
        
        if len(bmi_group) == 0:
            print(f"BMI {lower}-{upper} 组无有效数据")
            continue
        
        # 计算KM生存曲线
        kmf = KaplanMeierFitter()
        kmf.fit(bmi_group['达标孕周'], bmi_group['达标状态'])
        
        # 计算期望风险
        test_weeks = np.arange(11, 28)
        expected_risks = []
        
        for t in test_weeks:
            S_T = kmf.predict(t)
            risk = Risk(t, i, unhealthy_prob)  
            expected_risk = S_T * risk + (1 - S_T) * risk * 10
            expected_risks.append(expected_risk)
        
        # 找到最小期望风险对应的孕周
        optimal_time = test_weeks[np.argmin(expected_risks)]
        optimal_times[f'BMI_{lower}-{upper}'] = optimal_time
        
        # 可视化
        plt.figure(figsize=(12, 5))
        
        plt.subplot(1, 2, 1)
        plt.plot(test_weeks, [Risk(t, i, unhealthy_prob) for t in test_weeks], label='风险函数')
        plt.title(f'BMI {lower}-{upper} 风险函数')
        plt.xlabel('孕周')
        plt.ylabel('风险值')
        plt.legend()
        
        plt.subplot(1, 2, 2)
        plt.plot(test_weeks, expected_risks, label='期望风险')
        plt.axvline(x=optimal_time, color='r', linestyle='--', label=f'最佳时间: {optimal_time}周')
        plt.title(f'BMI {lower}-{upper} 期望风险曲线')
        plt.xlabel('孕周')
        plt.ylabel('期望风险')
        plt.legend()
        
        plt.tight_layout()
        plt.show()
    
    return optimal_times


if __name__ == '__main__':
    df = K_means(df1)
    KaplanMeier(df)
    optimal_times = FindTime(df, df2)
    print(optimal_times)

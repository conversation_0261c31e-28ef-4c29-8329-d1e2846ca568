import numpy as np
import matplotlib.pyplot as plt
from sklearn.linear_model import LinearRegression

# 参数配置区
v = 0.0125          # 质外体体积 (mL)
lambda_val = 0.002  # 温度响应系数
mu = 0.001          # 综合环境系数
eta = 0.005         # 温度响应调节因子
k_leaf = 0.3        # 叶片相关系数

# 时间参数
start_hour = 0      # 模拟开始时间
duration = 48       # 模拟总时长 (小时)
dt = 0.1            # 时间步长 (小时)
n_simulations = 1000  # 蒙特卡洛模拟次数

# 蒙特卡洛模拟参数范围
temperature_range = (20, 30)  # 摄氏度
humidity_range = (0.6, 0.8)   # 相对湿度 (小数形式)
turbulence_range = (0.05, 0.15)  # 湍流强度 (m/s)

# 模拟结果存储
tburst_results = []
Pcrit_results = []
dispersion_results = []

# 蒙特卡洛模拟
for _ in range(n_simulations):
    # 随机生成环境参数
    temp = np.random.uniform(*temperature_range)
    humidity = np.random.uniform(*humidity_range)
    turbulence = np.random.uniform(*turbulence_range)
    
    # 计算 tburst（简化模型，实际应基于问题2的模型）
    # 这里假设 tburst 与温度和湿度有关
    tburst = 48 - (temp - 20) * 2 + (humidity - 0.6) * 30
    tburst_results.append(tburst)
    
    # 计算 Pcrit（简化模型，实际应基于问题2的模型）
    # 这里假设 Pcrit 与温度和湿度有关
    Pcrit = 2e5 + (temp - 25) * 1e4 + (humidity - 0.7) * 2e5
    Pcrit_results.append(Pcrit)
    
    # 计算气溶胶散布范围（简化模型，实际应基于问题3的模型）
    # 这里假设散布范围与湍流强度有关
    dispersion = turbulence * 5
    dispersion_results.append(dispersion)

# 转换为 NumPy 数组
tburst_results = np.array(tburst_results)
Pcrit_results = np.array(Pcrit_results)
dispersion_results = np.array(dispersion_results)

# 敏感性分析 - 线性回归
def perform_sensitivity_analysis(results, param_name):
    X = np.column_stack((temperature_samples, humidity_samples, turbulence_samples))
    y = results
    
    model = LinearRegression()
    model.fit(X, y)
    
    print(f"{param_name} 的敏感性分析:")
    print(f"温度系数: {model.coef_[0]:.4f}")
    print(f"湿度系数: {model.coef_[1]:.4f}")
    print(f"湍流强度系数: {model.coef_[2]:.4f}")
    print(f"R²: {model.score(X, y):.4f}\n")

# 执行敏感性分析
temperature_samples = np.random.uniform(*temperature_range, n_simulations)
humidity_samples = np.random.uniform(*humidity_range, n_simulations)
turbulence_samples = np.random.uniform(*turbulence_range, n_simulations)

perform_sensitivity_analysis(tburst_results, "tburst")
perform_sensitivity_analysis(Pcrit_results, "Pcrit")
perform_sensitivity_analysis(dispersion_results, "Dispersion Range")
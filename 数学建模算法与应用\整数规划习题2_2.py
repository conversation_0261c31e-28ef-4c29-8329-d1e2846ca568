import pandas as pd
import pulp

#定义问题实例
prob = pulp.LpProblem('Practice2_2',pulp.LpMinimize)

#定义决策变量
x = pulp.LpVariable.dicts('x',range(1,7),cat = 'Binary')


#定义目标函数
prob += pulp.lpSum(x[i] for i in range(1,7)),"Objective"

#定义约束条件
prob += x[1] + x[2] + x[3] >= 1,"Constraint_1"
prob += x[2] + x[4] >= 1,"Constraint_2"
prob += x[3] + x[5] >= 1,"Constraint_3"
prob += x[4] + x[6] >= 1,"Constraint_4"
prob += x[5] + x[6] >= 1,"Constraint_5"
prob += x[1] >= 1,"Constraint_6"
prob += x[2] + x[4] + x[6] >= 1,"Constraint_7"

#求解问题
prob.solve()

#输出结果
print('Status:', pulp.LpStatus[prob.status])
print("所有变量取值：")
for i in range(1,7):  # 遍历所有变量
    val = x[i].varValue
    print(f"x_{i} = {val}")

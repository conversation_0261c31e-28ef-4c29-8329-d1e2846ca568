import pandas as pd
import numpy as np
from sklearn.tree import DecisionTreeRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 决策树回归
def decision_tree_regression(df):
    # 数据预处理
    df_clean = df.dropna(subset=['检测孕周', '孕妇BMI', 'Y染色体浓度'])

    # 特征选择
    features = ['孕妇BMI']
    X = df_clean[features]
    y = df_clean['检测孕周']  # 目标变量：最佳检测时间

    # 数据分割和模型训练
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    dt_reg = DecisionTreeRegressor(max_depth=5, random_state=42)
    dt_reg.fit(X_train, y_train)

    # 预测和评估
    y_pred = dt_reg.predict(X_test)
    mse = mean_squared_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)

    print("\n决策树回归结果：")
    print(f"均方误差 (MSE): {mse:.4f}")
    print(f"决定系数 (R²): {r2:.4f}")

    # 可视化预测结果
    plt.figure(figsize=(8, 6))
    plt.scatter(y_test, y_pred, alpha=0.6)
    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
    plt.xlabel('实际最佳检测时间')
    plt.ylabel('预测最佳检测时间')
    plt.title(f'决策树回归预测结果 (R² = {r2:.4f})')
    plt.tight_layout()
    plt.show()

    return dt_reg

# BMI分组最佳检测时间
def bmi_group_optimal_time(df):
    # 数据预处理
    df_clean = df.dropna(subset=['孕妇代码', '检测孕周', '孕妇BMI', 'Y染色体浓度'])

    # 定义BMI分组（按照题目要求）
    def get_bmi_group(bmi):
        if 20 <= bmi < 28:
            return '[20,28)'
        elif 28 <= bmi < 32:
            return '[28,32)'
        elif 32 <= bmi < 36:
            return '[32,36)'
        elif 36 <= bmi < 40:
            return '[36,40)'
        elif bmi >= 40:
            return '≥40'
        else:
            return '<20'  # BMI小于20的情况

    df_clean = df_clean.copy()
    df_clean['BMI组'] = df_clean['孕妇BMI'].apply(get_bmi_group)

    # 为每个孕妇找到首次达标时间
    individual_data = []
    for _, group in df_clean.groupby('孕妇代码'):
        group = group.sort_values('检测孕周')
        mask = group['Y染色体浓度'] > 0.04
        if mask.any():
            optimal_time = group[mask]['检测孕周'].iloc[0]
        else:
            optimal_time = group['检测孕周'].iloc[-1]

        info = group.iloc[0]
        individual_data.append({
            '孕妇BMI': info['孕妇BMI'],
            'BMI组': info['BMI组'],
            '最佳检测时间': optimal_time
        })

    individual_df = pd.DataFrame(individual_data)

    print("\nBMI分组统计：")
    for group_name in individual_df['BMI组'].unique():
        group_data = individual_df[individual_df['BMI组'] == group_name]
        print(f"\n{group_name}组:")
        print(f"  样本数: {len(group_data)}")
        print(f"  平均最佳检测时间: {group_data['最佳检测时间'].mean():.1f}周")

    # 使用决策树为每个BMI组找最佳时间
    optimal_times_by_group = {}

    for group_name in individual_df['BMI组'].unique():
        group_data = individual_df[individual_df['BMI组'] == group_name]

        # 特征：BMI
        features = ['孕妇BMI']
        X = group_data[features]
        y = group_data['最佳检测时间']

        if len(group_data) > 10:  # 确保有足够样本
            # 决策树回归
            dt = DecisionTreeRegressor(max_depth=5, random_state=42)
            dt.fit(X, y)

            # 预测该组的最佳时间（使用组内平均BMI）
            avg_bmi = X.mean().values.reshape(1, -1)
            predicted_time = dt.predict(avg_bmi)[0]

            optimal_times_by_group[group_name] = {
                '建议检测时间': round(predicted_time, 1),
                '样本数': len(group_data)
            }
        else:
            # 样本太少，使用平均值
            optimal_times_by_group[group_name] = {
                '建议检测时间': round(group_data['最佳检测时间'].mean(), 1),
                '样本数': len(group_data)
            }

    print("\n各BMI组最佳检测时间建议：")
    for group, info in optimal_times_by_group.items():
        print(f"\n{group}:")
        print(f"  建议检测时间: {info['建议检测时间']}周")
        print(f"  基于样本数: {info['样本数']}")

    return optimal_times_by_group, individual_df

def main():
    # 加载数据
    df = pd.read_excel(r'D:\数学建模\2025国赛C题\男胎数据.xlsx')
    print(f"数据形状: {df.shape}")

    # 1. 决策树回归任务：预测最佳检测时间
    decision_tree_regression(df)

    # 2. BMI分组最佳检测时间分析
    optimal_times, _ = bmi_group_optimal_time(df)

    # 总结建议
    print("基于决策树的BMI分组最佳检测时间")

    # 按BMI从小到大排序输出
    bmi_order = ['[20,28)', '[28,32)', '[32,36)', '[36,40)', '≥40']
    for group in bmi_order:
        if group in optimal_times:
            info = optimal_times[group]
            print(f"BMI {group}: {info['建议检测时间']}周 (样本数:{info['样本数']})")

if __name__ == '__main__':
    main()
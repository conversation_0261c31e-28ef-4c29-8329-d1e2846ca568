import numpy as np
import pandas as pd
import cvxpy as cp

#读取比赛报名表
match = pd.read_excel('整数规划例2_10_1.xlsx',header = None)

n = 14

#初始化邻接矩阵
w = np.full((n + 1,n + 1),1000000)

#构建邻接矩阵
for i in range(n):
    for j in range(n):
        if i != j:
            w[i,j] = sum(match.iloc[i,:] * match.iloc[j,:])

for i in range(n):
    w[n,i] = 0
    w[i,n] = 0

wd = pd.DataFrame(w)
wd.to_excel('整数规划例2_10_2.xlsx')

#定义变量
x = cp.Variable((n + 1,n + 1),integer = True)
u = cp.Variable(n + 1,integer = True)

#定义目标函数，最小化邻接矩阵和决策变量x的逐元素相乘后的总和
obj = cp.Minimize(cp.sum(cp.multiply(w,x)))

#定义约束条件
con = [cp.sum(x,axis = 0) == 1,cp.sum(x,axis = 1) == 1,x >= 0,x <= 1,u[0] == 0,u[1:] >= 1,u[1:] <= n]

#定义额外约束变量
for i in range(n + 1):
    for j in range(1,n + 1):
        con.append(u[i] - u[j] + (n + 1) * x[i,j] <= n)
#定义问题
prob = cp.Problem(obj,con)

#求解问题
prob.solve(solver = 'GLPK_MI')

#输出结果
print("最优值为：",prob.value)
print("最优解为：")
i,j = np.nonzero(x.value)
print(i + 1,j + 1)
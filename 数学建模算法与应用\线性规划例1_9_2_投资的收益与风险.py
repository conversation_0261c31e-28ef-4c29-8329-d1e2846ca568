import numpy as np
import pulp
import matplotlib.pyplot as plt

#资金
M = 10000
#平均收益率(r_i - p_i)
r = [0.05,0.27,0.19,0.185,0.185]
#风险损失率
q = [0,0.025,0.015,0.055,0.026]
#交易费费率(1 + p_i)
p = [1,1.01,1.02,1.045,1.065]
#投资阈值忽略不计

#定义模型2
def model_2(M,k,r,q,p):
    #创建问题实例
    #设置为最小化问题
    prob = pulp.LpProblem('Model_2',pulp.LpMinimize)

    #定义决策变量
    x = pulp.LpVariable.dicts('x',range(5),lowBound = 0,cat = 'Integer')
    risk = pulp.LpVariable('risk',lowBound = 0)

    #定义目标函数
    prob += risk,"Objective"

    #定义约束条件
    prob += pulp.lpSum(p[i] * x[i] for i in range(5)) == M,"Constraint1"
    prob += pulp.lpSum(r[i] * x[i] for i in range(5)) >= k * M ,"Constraint2"
    for i in range(5):
        prob += q[i] * x[i] <= risk,f"Constraint3_{i}"

    #求解问题
    prob.solve()

    #输出结果
    x_0,x_1,x_2,x_3,x_4 = [x[i].varValue for i in range(5)]
    risky = pulp.value(prob.objective)
    return x_0,x_1,x_2,x_3,x_4,risky

#可视化模型2的结果
def plot2():
    #定义数据表
    Data = np.zeros((7,51))
    i = 0
    for k in np.arange(0,0.51,0.01):
        x_0,x_1,x_2,x_3,x_4,risky = model_2(M,k,r,q,p)
        new_data = np.array([k,x_0,x_1,x_2,x_3,x_4,risky])
        Data[:,i] = new_data
        i += 1

    #定义x和y轴数据
    x_data = Data[0,:]
    y_data = Data[6,:]
    
    #绘制散点图
    plt.scatter(x_data,y_data,c = 'r',s = 5)
    plt.title("Model 2")
    plt.xlabel("k")
    plt.ylabel("risky")

    #显示图表
    plt.show()

plot2()
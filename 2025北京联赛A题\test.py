import numpy as np
import matplotlib.pyplot as plt

# 调整后的参数（延迟P值峰值至36小时）
v = 0.0125          # 质外体体积 (mL)
lambda_val = 0.002  # 温度响应系数
mu = 3e-11          # 综合环境系数
k_leaf = 0.3        # 叶片相关系数

# 时间参数
start_hour = 0      # 模拟开始时间
duration = 48       # 模拟总时长 (小时)
dt = 0.1            # 时间步长 (小时)

# 生成时间序列
t_hours = np.arange(start_hour, start_hour + duration + dt, dt)

# 温度相关函数（保持不变）
def fourier_model1(x, *params):
    a0, a1, b1, a2, b2, a3, b3 = params
    return a0 + a1*np.sin(x*np.pi/12) + b1*np.cos(x*np.pi/12) + \
           a2*np.sin(2*x*np.pi/12) + b2*np.cos(2*x*np.pi/12) + \
           a3*np.sin(3*x*np.pi/12) + b3*np.cos(3*x*np.pi/12)

# 湿度相关函数（新增独立模型）
def fourier_model_humidity(x, *params):
    a0, a1, b1, a2, b2, a3, b3 = params
    return a0 + a1*np.sin(x*np.pi/12) + b1*np.cos(x*np.pi/12) + \
           a2*np.sin(2*x*np.pi/12) + b2*np.cos(2*x*np.pi/12) + \
           a3*np.sin(3*x*np.pi/12) + b3*np.cos(3*x*np.pi/12)

# 逻辑斯蒂增长模型（保持不变）
def logistic_growth(N0, K, start_hour, duration, dt, popt):
    t_hours = np.arange(start_hour, start_hour + duration + dt, dt)  # 与主程序一致的时间点
    t_norm = t_hours % 24
    temp_series = fourier_model1(t_norm, *popt)
    
    N = [N0]
    for i in range(len(t_hours)-1):
        current_temp = temp_series[i]
        current_r = r_from_temp(current_temp)
        dN = current_r * N[i] * (1 - N[i]/K) * dt  # 乘以时间步长dt
        N.append(N[i] + dN)
    return N

# 温度-增殖速率关系（保持不变）
def r_from_temp(T):
    return -0.0002515*T**3 + 0.006988*T**2 + 0.09188*T - 0.4631

# 新增：P值计算函数（已修正PH处理）
def calculate_P(n, T, pH_percent):
    """
    计算P值的物理公式
    :param n: 菌群数量 (cells/mL)
    :param T: 温度 (℃)
    :param pH_percent: pH值百分比 (0-100%)
    :return: P值
    """
    pH = pH_percent / 100  # 将百分比转换为小数
    growth_term = (n / v) * (lambda_val * np.log(T) + mu * n)
    inhibition_term = k_leaf * (1 - pH)
    return growth_term - inhibition_term

# 主程序
if __name__ == "__main__":
    # 加载温度/湿度拟合参数（需替换为实际实验数据）
    popt_temp = [23.6059, -3.3371, -8.4442, 0.05928, 2.9215, 0.7351, -0.1901]
    popt_humidity = [64.5889, 10.5876, 29.9382, -4.9922, -8.4385, -7.4047, -3.2819]
    
    # 生成时间序列
    t_hours = np.arange(0, 48 + 0.1, 0.1)
    
    # 生成独立温度和湿度序列
    T_series = fourier_model1(t_hours, *popt_temp)
    pH_series = fourier_model_humidity(t_hours, *popt_humidity)  # 输出0-1之间的值
    pH_percent_series = pH_series * 100  # 转换为0-100%
    
    # 运行逻辑斯蒂增长模型
    N = logistic_growth(N0=1, K=1.5e6, start_hour=0, duration=48, dt=0.1, popt=popt_temp)
    
    # 计算P值序列
    P_series = []
    for i in range(len(t_hours)):
        n = N[i]
        T = T_series[i]
        pH_percent = pH_percent_series[i]
        P = calculate_P(n, T, pH_percent)
        P_series.append(P)
    
    # 绘制可视化图表
    plt.figure(figsize=(12, 6))
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
    plt.plot(t_hours, N, label='菌群数量 (cells/mL)', color='blue')
    plt.plot(t_hours, P_series, label='P值', color='red', linestyle='--')
    
    plt.title('菌群数量与P值随时间变化', fontsize=14)
    plt.xlabel('时间 (小时)', fontsize=12)
    plt.ylabel('值', fontsize=12)
    plt.legend()
    plt.tight_layout()
    plt.show()

    # 现在需要找到p值首次到到2x10^5的时间
    target_value = 2e5
    first_index = np.where(np.array(P_series) >= target_value)[0][0]
    print(f"P值首次达到2x10^5的时间为: {t_hours[first_index]:.2f} 小时")
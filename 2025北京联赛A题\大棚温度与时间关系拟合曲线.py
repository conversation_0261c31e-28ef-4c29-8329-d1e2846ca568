import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import curve_fit
from datetime import datetime

# 读取并预处理数据
df = pd.read_excel('D:/数学建模/2025北京联赛A题/附件1.xlsx')
df.columns = ['时间', '温度(℃)', '相对湿度RH（%）']
df['时间'] = df['时间'].apply(lambda x: datetime.strptime(x.strip(), '%H:%M:%S'))
df['小时'] = df['时间'].dt.hour + df['时间'].dt.minute/60  # 转换为小时数值

# 定义3阶傅里叶函数 (足够捕捉昼夜波动)
def fourier_model(x, a0, a1, b1, a2, b2, a3, b3):
    return a0 + a1*np.sin(1*np.pi*x/12) + b1*np.cos(1*np.pi*x/12) + \
           a2*np.sin(2*np.pi*x/12) + b2*np.cos(2*np.pi*x/12) + \
           a3*np.sin(3*np.pi*x/12) + b3*np.cos(3*np.pi*x/12)

# 执行参数拟合
initial_guess = [20] + [0]*6  # 初始猜测：平均温度20°C
popt, pcov = curve_fit(fourier_model, df['小时'], df['温度(℃)'], p0=initial_guess)

# 生成拟合曲线
x_fit = np.linspace(0, 24, 200)
y_fit = fourier_model(x_fit, *popt)

# 计算拟合优度
residuals = df['温度(℃)'] - fourier_model(df['小时'], *popt)
ss_res = np.sum(residuals**2)
ss_tot = np.sum((df['温度(℃)'] - np.mean(df['温度(℃)']))**2)
r2 = 1 - (ss_res / ss_tot)

# 可视化
plt.figure(figsize=(12,6))
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.scatter(df['小时'], df['温度(℃)'], s=20, alpha=0.6, label='原始数据')
plt.plot(x_fit, y_fit, 'r-', lw=2, label=f'傅里叶拟合 (R²={r2:.3f})')
plt.title('温室温度日变化拟合曲线', fontsize=14)
plt.xlabel('时间 (小时)', fontsize=12)
plt.ylabel('温度 (℃)', fontsize=12)
plt.xticks(np.arange(0, 25, 3))
plt.grid(alpha=0.3)
plt.legend()
plt.tight_layout()
plt.show()

# 输出拟合参数
print("拟合参数：", popt)
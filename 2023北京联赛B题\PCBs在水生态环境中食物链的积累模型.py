import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import odeint

# 参数定义
params = {
    # 种群增长参数（基于文献估算）
    'r1': 0.1,       # 水蚤的日增长率
    'r2': 0.05,      # 蓝腮太阳鱼的日增长率
    'r3': 0.03,      # 大口黑鲈鱼的日增长率
    
    # 捕食率参数（基于AQUATOX模型估算）
    'a12': 0.0005,   # 蓝腮太阳鱼捕食水蚤的效率
    'a23': 0.0002,   # 大口黑鲈鱼捕食蓝腮太阳鱼的效率
    
    # 捕食者死亡率参数（基础值）
    'm2_base': 0.02, # 蓝腮太阳鱼的基础日死亡率
    'm3_base': 0.01, # 大口黑鲈鱼的基础日死亡率
    
    # 污染物影响参数
    'k1': 0.1,       # 水蚤对PCBs的敏感系数（基于LC50=1.8-24 μg/L）
    'k2': 0.05,      # 蓝腮太阳鱼对PCBs的敏感系数（基于LC50=125 μg/L）
    'k3': 0.03,      # 大口黑鲈鱼对PCBs的敏感系数（基于LC50=150 μg/L）
    
    # 污染物积累参数（基于生物积累系数BAF估算）
    'BAF1': 3000,    # 水蚤的生物积累系数
    'BAF2': 6000,    # 蓝腮太阳鱼的生物积累系数
    'BAF3': 9000,    # 大口黑鲈鱼的生物积累系数
    
    # 污染物排出速率
    'e1': 0.1,       # 水蚤排出PCBs的速率
    'e2': 0.07,      # 蓝腮太阳鱼排出PCBs的速率
    'e3': 0.05       # 大口黑鲈鱼排出PCBs的速率
}

# 初始条件
initial_conditions = {
    'prey': 1000,    # 水蚤初始数量
    'predator1': 100,# 蓝腮太阳鱼初始数量
    'predator2': 20, # 大口黑鲈鱼初始数量
    
    'C_water': 0.0,  # 水中初始PCBs浓度 (μg/L)
    'C1': 0.0,       # 水蚤体内初始PCBs浓度
    'C2': 0.0,       # 蓝腮太阳鱼体内初始PCBs浓度
    'C3': 0.0        # 大口黑鲈鱼体内初始PCBs浓度
}

# 时间范围（天）
t = np.linspace(0, 365*5, 1000)  # 5年模拟

def food_chain_model(state, t, params, PCBs_concentration):
    # 解包状态变量
    x, y, z, C1, C2, C3 = state
    
    # 计算死亡率（考虑PCBs影响）
    m1 = params['k1'] * PCBs_concentration  # 水蚤死亡率
    m2 = params['m2_base'] + params['k2'] * C2  # 蓝腮太阳鱼死亡率
    m3 = params['m3_base'] + params['k3'] * C3  # 大口黑鲈鱼死亡率
    
    # 种群动态方程（Lotka-Volterra模型扩展版）
    dxdt = params['r1'] * x * (1 - x/10000) - params['a12'] * x * y - m1 * x
    dydt = params['a12'] * x * y - params['r2'] * y - params['a23'] * y * z - m2 * y
    dzdt = params['a23'] * y * z - params['r3'] * z - m3 * z
    
    # 污染物积累模型
    dC1dt = (params['BAF1'] * PCBs_concentration - params['e1'] * C1)
    dC2dt = (params['BAF2'] * (PCBs_concentration + params['a12'] * x * C1 / (params['a12'] * x + m2)) - params['e2'] * C2)
    dC3dt = (params['BAF3'] * (params['a23'] * y * C2 / (params['a23'] * y + m3)) - params['e3'] * C3)
    
    return [dxdt, dydt, dzdt, dC1dt, dC2dt, dC3dt]

# 模拟不同PCBs浓度情况
PCBs_scenarios = {
    '0%': 0.0,               # 无污染
    '10% mortality': 0.5,    # 导致水蚤死亡率10%的PCBs浓度 (μg/L)
    '20% mortality': 1.0     # 导致水蚤死亡率20%的PCBs浓度 (μg/L)
}

# 运行模拟并绘制结果
plt.figure(figsize=(15, 10))

for scenario, concentration in PCBs_scenarios.items():
    # 初始状态
    state0 = [
        initial_conditions['prey'],
        initial_conditions['predator1'],
        initial_conditions['predator2'],
        initial_conditions['C1'],
        initial_conditions['C2'],
        initial_conditions['C3']
    ]
    
    # 求解微分方程
    solution = odeint(food_chain_model, state0, t, args=(params, concentration))
    
    # 提取结果
    x, y, z, C1, C2, C3 = solution.T
    
    # 绘制种群数量变化
    plt.subplot(2, 2, 1)
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
    plt.plot(t/365, x, label=f'水蚤 ({scenario})')
    plt.title('水蚤种群数量')
    plt.xlabel('年份')
    plt.ylabel('种群数量')
    plt.legend()
    
    plt.subplot(2, 2, 2)
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
    plt.plot(t/365, y, label=f'蓝腮太阳鱼 ({scenario})')
    plt.title('蓝腮太阳鱼种群数量')
    plt.xlabel('年份')
    plt.ylabel('种群数量')
    plt.legend()
    
    plt.subplot(2, 2, 3)
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
    plt.plot(t/365, z, label=f'大口黑鲈鱼 ({scenario})')
    plt.title('大口黑鲈鱼种群数量')
    plt.xlabel('年份')
    plt.ylabel('种群数量')
    plt.legend()
    
    plt.subplot(2, 2, 4)
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
    plt.plot(t/365, C1, label=f'水蚤PCBs浓度 ({scenario})')
    plt.plot(t/365, C2, label=f'蓝腮太阳鱼PCBs浓度 ({scenario})')
    plt.plot(t/365, C3, label=f'大口黑鲈鱼PCBs浓度 ({scenario})')
    plt.title('生物体内PCBs浓度')
    plt.xlabel('年份')
    plt.ylabel('PCBs浓度 (μg/kg)')
    plt.legend()

plt.tight_layout()
plt.show()
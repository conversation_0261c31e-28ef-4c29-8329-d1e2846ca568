import random

#定义目标函数
def objective_function(x_1,x_2,x_3,x_4,x_5):
    return x_1**2 + x_2**2 + 3 * x_3**2 + 4 * x_4**2 + 2 * x_5**2 - 8 * x_1 - 2 * x_2 - 3 * x_3 - x_4 - 2 * x_5

#定义约束条件
def constraint_function(x_1,x_2,x_3,x_4,x_5):
    return x_1 + x_2 + x_3 + x_4 + x_5 <= 400 and x_1 + 2 * x_2 + 2 * x_3 + x_4 + 6 * x_5 <= 800 and 2 * x_1 + x_2 + 6 * x_3 <= 200 and x_3 + x_4 +5 * x_5 <= 200 and 0 <= x_1 < 100 and 0 <= x_2 < 100 and 0 <= x_3 < 100 and 0 <= x_4 < 100 and 0 <= x_5 < 100

#蒙特卡洛法求解问题
def monte_carlo_Optimizaation(num_samples):
    best_solution = None
    best_value = float('-inf')
    for _ in  range(num_samples):
        x_1 = random.randint(0,99)
        x_2 = random.randint(0,99)
        x_3 = random.randint(0,99)
        x_4 = random.randint(0,99)
        x_5 = random.randint(0,99)
        
        #检查是否满足约束条件
        if constraint_function(x_1,x_2,x_3,x_4,x_5):
            current_value = objective_function(x_1,x_2,x_3,x_4,x_5)
            
            if current_value > best_value:
                best_value = current_value
                best_solution = (x_1,x_2,x_3,x_4,x_5)
        
    return best_solution,best_value

#设置随机种子
random.seed(42)


#设置样本数量
num_samples = 1000000

#求解问题
best_solution,best_value = monte_carlo_Optimizaation(num_samples)

#输出结果
print("Best solution:",best_solution)
print("Best value:",best_value)
import numpy as np
import matplotlib.pyplot as plt

# 参数配置区（保持不变）
v = 0.0125          # 质外体体积 (mL)
lambda_val = 0.002  # 温度响应系数
mu = 3e-11          # 综合环境系数
k_leaf = 0.3        # 叶片相关系数

# 时间参数（修改为36小时）
start_hour = 0      # 模拟开始时间
duration = 36       # 模拟总时长 (小时)
dt = 0.1            # 时间步长 (小时)

# 生成时间序列
t_hours = np.arange(start_hour, start_hour + duration + dt, dt)

# 温度相关函数（保持不变）
def fourier_model1(x, *params):
    a0, a1, b1, a2, b2, a3, b3 = params
    return a0 + a1*np.sin(x*np.pi/12) + b1*np.cos(x*np.pi/12) + \
           a2*np.sin(2*x*np.pi/12) + b2*np.cos(2*x*np.pi/12) + \
           a3*np.sin(3*x*np.pi/12) + b3*np.cos(3*x*np.pi/12)

# 湿度相关函数（保持不变）
def fourier_model_humidity(x, *params):
    a0, a1, b1, a2, b2, a3, b3 = params
    return a0 + a1*np.sin(x*np.pi/12) + b1*np.cos(x*np.pi/12) + \
           a2*np.sin(2*x*np.pi/12) + b2*np.cos(2*x*np.pi/12) + \
           a3*np.sin(3*x*np.pi/12) + b3*np.cos(3*x*np.pi/12)

# 逻辑斯蒂增长模型（保持不变）
def logistic_growth(N0, K, start_hour, duration, popt):
    t_hours = np.linspace(start_hour, start_hour + duration, int(duration/dt)+1)
    t_norm = t_hours % 24
    temp_series = fourier_model1(t_norm, *popt)
    
    N = [N0]
    for i in range(len(t_hours)-1):
        current_temp = temp_series[i]
        current_r = r_from_temp(current_temp)
        dN = current_r * N[i] * (1 - N[i]/K) * dt
        N.append(N[i] + dN)
    return N

# 温度-增殖速率关系（保持不变）
def r_from_temp(T):
    return -0.0002515*T**3 + 0.006988*T**2 + 0.09188*T - 0.4631

# P值计算函数（保持不变）
def calculate_P(n, T, pH_percent):
    pH = pH_series[i]
    growth_term = (n / v) * (lambda_val * np.log(T) + mu * n)
    inhibition_term = k_leaf * (1 - pH)
    return growth_term - inhibition_term

# 主程序
if __name__ == "__main__":
    # 加载温度/湿度拟合参数（需替换为实际实验数据）
    popt_temp = [23.6059, -3.3371, -8.4442, 0.05928, 2.9215, 0.7351, -0.1901]
    popt_humidity = [64.5889, 10.5876, 29.9382, -4.9922, -8.4385, -7.4047, -3.2819]
    
    # 生成时间序列（36小时）
    t_hours = np.arange(0, 36 + 0.1, 0.1)
    
    # 生成环境参数序列
    T_series = fourier_model1(t_hours, *popt_temp)
    pH_series = fourier_model_humidity(t_hours, *popt_humidity)
    pH_percent_series = pH_series * 100
    
    # 运行逻辑斯蒂增长模型（36小时）
    N = logistic_growth(N0=1, K=1.5e6, start_hour=0, duration=36, popt=popt_temp)
    
    # 计算P值序列
    P_series = []
    for i in range(len(t_hours)):
        n = N[i]
        T = T_series[i]
        pH_pcnt = pH_percent_series[i]
        P = calculate_P(n, T, pH_pcnt)
        P_series.append(P)
    
    # 提取36小时时的P值作为Pcrit
    target_time = 36
    idx = np.searchsorted(t_hours, target_time)
    Pcrit = P_series[idx]
    
    # 输出结果
    print(f"健康叶片感染后36小时形成菌脓的临界压强为：Pcrit = {Pcrit:.2f} Pa")
    
    # 可视化验证（可选）
    plt.figure(figsize=(12,6))
    # 微软雅黑字体设置
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
    plt.rcParams['axes.unicode_minus'] = False
    plt.plot(t_hours, P_series, label='P值动态')
    plt.axhline(Pcrit, color='r', linestyle='--', label=f'临界压强 {Pcrit:.2f} Pa')
    plt.axvline(target_time, color='g', linestyle=':', label='36小时观测点')
    plt.xlabel('时间 (小时)')
    plt.ylabel('P值 (Pa)')
    plt.title('菌脓破裂临界压强确定')
    plt.legend()
    plt.grid(True)
    plt.show()

    #检查一下p的最大值
    print(f"P的最大值为：{max(P_series):.2f} Pa")
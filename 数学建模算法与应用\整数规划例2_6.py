import pulp

#费用
cost = [[15,13.8,12.5,11,14.3],
        [14.5,14,13.2,10.5,15],
        [13.8,13,12.8,11.3,14.6],
        [14.7,13.6,13,11.6,14]]

#定义问题实例
prob = pulp.LpProblem('Practice2_6',pulp.LpMinimize)

#定义变量
x = pulp.LpVariable.dicts('x',[(i,j) for i in range(4) for j in range(5)],lowBound=0,cat='Binary')

#定义目标函数
prob += pulp.lpSum([cost[i][j]*x[(i,j)] for i in range(4) for j in range(5)]),"Objective"

# 定义约束条件
for i in range(4):
    prob += pulp.lpSum(x[(i,j)] for j in range(5)) <= 2, f"Constraint_1_{i+1}"
for j in range(5):
    prob += pulp.lpSum(x[(i,j)] for i in range(4)) == 1, f"Constraint_2_{j+1}"

#求解问题
prob.solve()

#输出结果
print('Status:',pulp.LpStatus[prob.status])
print("Optimal solution:")
for i in range(4):
    for j in range(5):
        print(f"x_{i+1}_{j+1} = {x[(i,j)].varValue}")
print(f"cost = {pulp.value(prob.objective)}")
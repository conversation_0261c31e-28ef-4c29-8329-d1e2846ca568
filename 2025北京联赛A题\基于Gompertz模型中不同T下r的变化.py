import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import curve_fit

# 原始数据（温度: 时间序列的细菌数量）
data = {
    "20℃": [0, 2033.33333333333, 13100, 182444.444444444, 1101500, 4933333.33333333, 
           60800000, 106666666.666667, 491500000, 749909090.909091, 771666666.666667],
    "25℃": [0, 3133.33333333333, 16900, 144000, 431222.222222222, 14500000, 
           139000000, 319666666.666667, 735500000, 820909090.909091, 852666666.666667],
    "28℃": [0, 3133.33333333333, 13900, 124000, 764000, 5383333.33333333, 
           77500000, 175666666.666667, 671666666.666667, 836666666.666667, 824166666.666667],
    "30℃": [0, 1863.33333333333, 5893.33333333333, 12500, 268000, 938000, 
           23833333.3333333, 53833333.3333333, 306666666.666667, 646666666.666667, 746666666.666667]
}

# Gompertz模型函数（对数尺度）
def gompertz(t, A, mu, lam):
    """Gompertz生长模型参数说明:
       A : 最大对数生物量(ln(N_max/N0))
       mu : 最大比生长速率(1/h)
       lam : 迟滞期时长(h)
    """
    return A * np.exp(-np.exp( (mu * np.e) / A * (lam - t) + 1 ))

# 准备画布
plt.figure(figsize=(14, 8))
results = {}

for temp, counts in data.items():
    # 数据预处理（过滤0值并取对数）
    t_data = []  # 时间（小时）
    y_data = []  # ln(N(t)/N0)，假设N0=1
    
    for i, count in enumerate(counts):
        if count > 1:  # 过滤无效数据点
            t = i * 3   # 转换为小时单位（每3小时记录一次）
            ln_count = np.log(count)
            t_data.append(t)
            y_data.append(ln_count)
    
    if len(t_data) < 3:
        print(f"{temp} 有效数据点不足，无法拟合")
        continue
    
    # 参数初始估计（关键步骤）
    A0 = max(y_data) * 1.05  # 最大生物量估计
    mu0 = (y_data[-1] - y_data[0]) / t_data[-1]  # 平均生长速率
    lam0 = t_data[np.argmax(np.diff(y_data) > 0.1)]  # 迟滞期结束点
    
    try:
        # 非线性最小二乘拟合
        params, cov = curve_fit(gompertz, t_data, y_data, 
                               p0=[A0, mu0, lam0],
                               maxfev=5000)
        A, mu, lam = params
        
        # 计算最大生长速率出现时间
        t_peak = lam + A/(mu * np.e)
        r_max = mu * np.exp(1)  # 最大生长速率（Gompertz定义）
        
        # 存储结果
        results[temp] = {
            'r_max': r_max,
            'mu': mu,
            'A': A,
            'lam': lam,
            't_peak': t_peak
        }
        
        # 绘制结果
        t_range = np.linspace(0, 30, 100)
        y_fit = gompertz(t_range, A, mu, lam)
        plt.scatter(t_data, y_data, s=80, label=f'{temp} 观测值')
        plt.plot(t_range, y_fit, lw=2, 
                label=f'{temp}拟合: r_max={r_max:.3f}/h\nλ={lam:.1f}h')
        
    except RuntimeError:
        print(f"{temp} 拟合失败，尝试调整初始参数")
        continue

# 图形美化
plt.title('Gompertz模型拟合不同温度下病原菌生长曲线', fontsize=14)
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.xlabel('时间 (小时)', fontsize=12)
plt.ylabel('ln(细菌数量)', fontsize=12)
plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
plt.grid(alpha=0.3)
plt.tight_layout()
plt.show()

# 输出详细结果
print("\n各温度下Gompertz模型参数：")
for temp, res in results.items():
    print(f"\n{temp}:")
    print(f"  最大增殖速率 r_max = {res['r_max']:.4f} /小时")
    print(f"  迟滞期时长 λ = {res['lam']:.1f} 小时")
    print(f"  最大对数生物量 A = {res['A']:.2f}")
    print(f"  峰值时间 t_peak = {res['t_peak']:.1f} 小时")